"""
URL验证器
验证BOSS直聘岗位URL的有效性和真实性
"""

import asyncio
import re
import time
from typing import List, Set, Dict, Any
from urllib.parse import urlparse, parse_qs
import aiohttp

class URLValidator:
    """URL验证器"""
    
    def __init__(self):
        self.valid_pattern = re.compile(r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9]+\.html')
        self.validated_urls: Set[str] = set()
        self.invalid_urls: Set[str] = set()
        
    def is_valid_format(self, url: str) -> bool:
        """检查URL格式是否有效"""
        if not url or not isinstance(url, str):
            return False
            
        # 检查基本格式
        if not self.valid_pattern.match(url.strip()):
            return False
            
        # 检查URL结构
        try:
            parsed = urlparse(url)
            if parsed.netloc != 'www.zhipin.com':
                return False
            if not parsed.path.startswith('/job_detail/'):
                return False
            if not parsed.path.endswith('.html'):
                return False
            return True
        except Exception:
            return False
    
    def extract_job_id(self, url: str) -> str:
        """从URL中提取岗位ID"""
        try:
            if not self.is_valid_format(url):
                return ""
            
            # 提取job_detail/后面的ID部分
            match = re.search(r'/job_detail/([a-zA-Z0-9]+)\.html', url)
            if match:
                return match.group(1)
            return ""
        except Exception:
            return ""
    
    def deduplicate_urls(self, urls: List[str]) -> List[str]:
        """URL去重"""
        seen_ids = set()
        unique_urls = []
        
        for url in urls:
            if not self.is_valid_format(url):
                continue
                
            job_id = self.extract_job_id(url)
            if job_id and job_id not in seen_ids:
                seen_ids.add(job_id)
                unique_urls.append(url)
        
        return unique_urls
    
    async def check_url_accessibility(self, url: str, session: aiohttp.ClientSession) -> bool:
        """检查URL可访问性"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
            
            async with session.head(url, headers=headers, timeout=10) as response:
                # 检查状态码
                if response.status == 200:
                    return True
                elif response.status in [301, 302, 307, 308]:
                    # 重定向，检查重定向目标
                    location = response.headers.get('Location', '')
                    if location and 'zhipin.com' in location:
                        return True
                return False
                
        except Exception as e:
            print(f"检查URL可访问性失败 {url}: {e}")
            return False
    
    async def batch_validate_urls(self, urls: List[str], max_concurrent: int = 10) -> Dict[str, List[str]]:
        """批量验证URL"""
        print(f"🔍 开始验证 {len(urls)} 个URL...")
        
        # 格式验证和去重
        valid_format_urls = [url for url in urls if self.is_valid_format(url)]
        unique_urls = self.deduplicate_urls(valid_format_urls)
        
        print(f"📋 格式验证通过: {len(valid_format_urls)}/{len(urls)}")
        print(f"🔄 去重后剩余: {len(unique_urls)}")
        
        if not unique_urls:
            return {"valid": [], "invalid": urls}
        
        # 可访问性验证
        valid_urls = []
        invalid_urls = []
        
        connector = aiohttp.TCPConnector(limit=max_concurrent, limit_per_host=5)
        timeout = aiohttp.ClientTimeout(total=30)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def validate_single_url(url: str):
                async with semaphore:
                    try:
                        is_accessible = await self.check_url_accessibility(url, session)
                        if is_accessible:
                            valid_urls.append(url)
                            self.validated_urls.add(url)
                        else:
                            invalid_urls.append(url)
                            self.invalid_urls.add(url)
                    except Exception as e:
                        print(f"验证URL异常 {url}: {e}")
                        invalid_urls.append(url)
                    
                    # 添加延迟避免请求过快
                    await asyncio.sleep(0.1)
            
            # 并发验证
            tasks = [validate_single_url(url) for url in unique_urls]
            await asyncio.gather(*tasks, return_exceptions=True)
        
        print(f"✅ 验证完成 - 有效: {len(valid_urls)}, 无效: {len(invalid_urls)}")
        
        return {
            "valid": valid_urls,
            "invalid": invalid_urls + [url for url in urls if not self.is_valid_format(url)]
        }
    
    def filter_recent_jobs(self, urls: List[str], days: int = 7) -> List[str]:
        """过滤最近发布的岗位（基于URL中的时间戳，如果有的话）"""
        # BOSS直聘的URL中通常不包含明确的时间戳
        # 这里可以根据实际情况实现更复杂的过滤逻辑
        return urls
    
    def get_validation_stats(self) -> Dict[str, int]:
        """获取验证统计信息"""
        return {
            "validated_count": len(self.validated_urls),
            "invalid_count": len(self.invalid_urls),
            "total_processed": len(self.validated_urls) + len(self.invalid_urls)
        }
    
    def save_validation_cache(self, filename: str = "url_validation_cache.txt"):
        """保存验证缓存"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("# 有效URL\n")
                for url in sorted(self.validated_urls):
                    f.write(f"VALID: {url}\n")
                
                f.write("\n# 无效URL\n")
                for url in sorted(self.invalid_urls):
                    f.write(f"INVALID: {url}\n")
                    
            print(f"✅ 验证缓存已保存到 {filename}")
        except Exception as e:
            print(f"❌ 保存验证缓存失败: {e}")
    
    def load_validation_cache(self, filename: str = "url_validation_cache.txt"):
        """加载验证缓存"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith("VALID: "):
                        url = line[7:]
                        self.validated_urls.add(url)
                    elif line.startswith("INVALID: "):
                        url = line[9:]
                        self.invalid_urls.add(url)
                        
            print(f"✅ 验证缓存已加载: 有效{len(self.validated_urls)}, 无效{len(self.invalid_urls)}")
        except FileNotFoundError:
            print(f"验证缓存文件 {filename} 不存在")
        except Exception as e:
            print(f"❌ 加载验证缓存失败: {e}")
