"""
BOSS直聘URL收集器
自动获取所有真实招聘岗位网址
"""

import asyncio
import time
import re
import json
from typing import List, Dict, Set, Any, Optional
from urllib.parse import urljoin, urlencode
import sys
import os

# 添加父目录到路径以导入现有模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from boss_security_bypass import BOSSSecurityBypass
from .search_config import SearchConfig
from .url_validator import URLValidator

try:
    from DrissionPage import ChromiumPage, ChromiumOptions
    DRISSION_PAGE_AVAILABLE = True
except ImportError:
    DRISSION_PAGE_AVAILABLE = False

class URLCollector:
    """BOSS直聘URL收集器"""
    
    def __init__(self):
        self.security_bypass = BOSSSecurityBypass()
        self.search_config = SearchConfig()
        self.url_validator = URLValidator()
        self.collected_urls: Set[str] = set()
        self.failed_searches: List[Dict] = []
        
    async def extract_job_urls_from_page(self, page_content: str) -> List[str]:
        """从页面内容中提取岗位URL"""
        urls = []
        
        try:
            # 多种URL提取模式
            patterns = [
                r'href="(/job_detail/[a-zA-Z0-9]+\.html)"',
                r'href="(https://www\.zhipin\.com/job_detail/[a-zA-Z0-9]+\.html)"',
                r'"url":"(/job_detail/[a-zA-Z0-9]+\.html)"',
                r'"jobUrl":"(/job_detail/[a-zA-Z0-9]+\.html)"'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, page_content)
                for match in matches:
                    if match.startswith('/'):
                        url = f"https://www.zhipin.com{match}"
                    else:
                        url = match
                    
                    if self.url_validator.is_valid_format(url):
                        urls.append(url)
            
            # 去重
            urls = list(set(urls))
            
        except Exception as e:
            print(f"❌ 提取URL失败: {e}")
        
        return urls
    
    async def get_search_page_content(self, search_url: str) -> str:
        """获取搜索页面内容，处理安全检查"""
        if not DRISSION_PAGE_AVAILABLE:
            print("❌ DrissionPage不可用")
            return ""

        try:
            from DrissionPage import ChromiumPage, ChromiumOptions

            # 配置Chrome选项
            options = ChromiumOptions()
            options.set_argument('--no-sandbox')
            options.set_argument('--disable-dev-shm-usage')
            options.set_argument('--disable-blink-features=AutomationControlled')
            options.set_argument('--disable-web-security')
            options.set_user_agent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # 启动浏览器
            page = ChromiumPage(addr_or_opts=options)

            try:
                print(f"🌐 访问搜索页面: {search_url}")
                page.get(search_url)

                # 等待页面加载
                await asyncio.sleep(3)

                # 检查是否是安全检查页面
                if "请稍候" in page.title or "security-check" in page.url:
                    print("🔒 检测到安全检查页面，等待跳转...")

                    # 等待最多30秒让安全检查完成
                    for i in range(30):
                        await asyncio.sleep(1)

                        current_title = page.title
                        current_url = page.url

                        # 检查是否已经跳转到正常页面
                        if ("招聘" in current_title or "BOSS直聘" in current_title) and "security-check" not in current_url:
                            print(f"✅ 安全检查完成！当前页面: {current_title}")
                            break

                        if i % 5 == 0:
                            print(f"⏳ 等待中... {i+1}/30")

                # 获取最终页面内容
                final_html = page.html
                print(f"📄 获取页面内容，长度: {len(final_html)}")

                return final_html

            finally:
                page.quit()

        except Exception as e:
            print(f"❌ 获取搜索页面失败: {e}")
            return ""

    async def search_jobs_by_params(self, search_params: Dict[str, Any], max_pages: int = 10) -> List[str]:
        """根据搜索参数获取岗位URL"""
        all_urls = []

        try:
            print(f"🔍 搜索参数: {search_params}")

            for page in range(1, max_pages + 1):
                print(f"📄 正在抓取第 {page} 页...")

                # 构建搜索URL
                params = search_params.copy()
                params['page'] = page

                # 修正URL格式 - 使用正确的搜索端点
                base_url = "https://www.zhipin.com/web/geek/jobs"
                search_url = f"{base_url}?{urlencode(params)}"

                # 获取页面内容
                page_content = await self.get_search_page_content(search_url)

                if not page_content:
                    print(f"❌ 第 {page} 页获取失败，跳过")
                    continue

                # 提取URL
                page_urls = await self.extract_job_urls_from_page(page_content)

                if not page_urls:
                    print(f"⚠️  第 {page} 页没有找到岗位URL，可能已到最后一页")
                    break

                all_urls.extend(page_urls)
                print(f"✅ 第 {page} 页获取到 {len(page_urls)} 个URL")

                # 请求间隔
                await asyncio.sleep(3)

                # 如果连续几页都没有新URL，可能已经到底了
                if page > 3 and len(set(page_urls)) < 5:
                    print(f"⚠️  第 {page} 页URL数量较少，可能已接近搜索结果末尾")
                    break

        except Exception as e:
            print(f"❌ 搜索失败: {e}")
            self.failed_searches.append({
                "params": search_params,
                "error": str(e),
                "timestamp": time.time()
            })

        return all_urls
    
    async def collect_all_job_urls(self, max_pages_per_search: int = 20, 
                                 max_concurrent_searches: int = 3) -> List[str]:
        """收集所有岗位URL"""
        print("🚀 开始收集BOSS直聘所有岗位URL...")
        print("=" * 60)
        
        # 获取搜索配置
        search_configs = self.search_config.get_comprehensive_search_configs()
        print(f"📊 总搜索配置数: {len(search_configs)}")
        
        all_urls = []
        semaphore = asyncio.Semaphore(max_concurrent_searches)
        
        async def process_search_config(config):
            async with semaphore:
                try:
                    urls = await self.search_jobs_by_params(config, max_pages_per_search)
                    return urls
                except Exception as e:
                    print(f"❌ 搜索配置处理失败: {e}")
                    return []
        
        # 分批处理搜索配置
        batch_size = 5
        for i in range(0, len(search_configs), batch_size):
            batch = search_configs[i:i + batch_size]
            print(f"\n📦 处理批次 {i//batch_size + 1}/{(len(search_configs)-1)//batch_size + 1}")
            
            tasks = [process_search_config(config) for config in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in batch_results:
                if isinstance(result, list):
                    all_urls.extend(result)
                else:
                    print(f"⚠️  批次处理异常: {result}")
            
            print(f"✅ 当前累计URL数: {len(all_urls)}")
            
            # 批次间隔
            await asyncio.sleep(5)
        
        # 去重
        unique_urls = self.url_validator.deduplicate_urls(all_urls)
        self.collected_urls.update(unique_urls)
        
        print(f"\n🎯 收集完成!")
        print(f"📊 原始URL数: {len(all_urls)}")
        print(f"📊 去重后URL数: {len(unique_urls)}")
        print(f"📊 失败搜索数: {len(self.failed_searches)}")
        
        return unique_urls
    
    async def collect_urls_by_keywords(self, keywords: List[str], cities: List[str] = None, 
                                     max_pages: int = 10) -> List[str]:
        """根据关键词和城市收集URL"""
        if cities is None:
            cities = ["全国"]
        
        all_urls = []
        
        for keyword in keywords:
            for city in cities:
                print(f"🔍 搜索: {city} - {keyword}")
                
                search_params = self.search_config.get_search_params(
                    city=city, keyword=keyword
                )
                
                urls = await self.search_jobs_by_params(search_params, max_pages)
                all_urls.extend(urls)
                
                print(f"✅ 获取到 {len(urls)} 个URL")
                await asyncio.sleep(3)
        
        return self.url_validator.deduplicate_urls(all_urls)
    
    def save_urls_to_file(self, urls: List[str], filename: str = "boss_job_urls.txt"):
        """保存URL到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                for url in sorted(urls):
                    f.write(f"{url}\n")
            
            print(f"✅ 已保存 {len(urls)} 个URL到 {filename}")
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
    
    def save_collection_report(self, urls: List[str], filename: str = "collection_report.json"):
        """保存收集报告"""
        try:
            report = {
                "timestamp": time.time(),
                "total_urls": len(urls),
                "unique_urls": len(set(urls)),
                "failed_searches": len(self.failed_searches),
                "search_configs_used": len(self.search_config.get_comprehensive_search_configs()),
                "collection_stats": self.url_validator.get_validation_stats(),
                "failed_search_details": self.failed_searches
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 收集报告已保存到 {filename}")
            
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
    
    async def quick_collect(self, max_urls: int = 1000) -> List[str]:
        """快速收集模式 - 获取指定数量的URL"""
        print(f"⚡ 快速收集模式 - 目标: {max_urls} 个URL")
        
        # 使用热门关键词和主要城市
        hot_keywords = ["Python", "Java", "前端", "产品经理"]
        major_cities = ["北京", "上海", "广州", "深圳"]
        
        all_urls = []
        
        for keyword in hot_keywords:
            if len(all_urls) >= max_urls:
                break
                
            for city in major_cities:
                if len(all_urls) >= max_urls:
                    break
                
                search_params = self.search_config.get_search_params(
                    city=city, keyword=keyword
                )
                
                urls = await self.search_jobs_by_params(search_params, max_pages=5)
                all_urls.extend(urls)
                
                print(f"✅ {city}-{keyword}: {len(urls)} 个URL, 累计: {len(all_urls)}")
                
                if len(all_urls) >= max_urls:
                    break
        
        # 去重并限制数量
        unique_urls = self.url_validator.deduplicate_urls(all_urls)
        return unique_urls[:max_urls]
