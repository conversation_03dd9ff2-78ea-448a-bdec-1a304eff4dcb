"""
BOSS直聘搜索配置管理
管理搜索参数、城市代码、职位类别等配置
"""

from typing import Dict, List, Any
import json

class SearchConfig:
    """搜索配置管理器"""
    
    def __init__(self):
        # 主要城市代码 (BOSS直聘城市ID)
        self.cities = {
            "全国": "100010000",
            "北京": "101010100", 
            "上海": "101020100",
            "广州": "101280100",
            "深圳": "101280600",
            "杭州": "101210100",
            "成都": "101270100",
            "武汉": "101200100",
            "西安": "101110100",
            "南京": "101190100",
            "苏州": "101190400",
            "天津": "101030100",
            "重庆": "101040100",
            "青岛": "101120200",
            "大连": "101090200",
            "厦门": "101230200",
            "宁波": "101210300",
            "佛山": "101280800",
            "无锡": "101190200",
            "合肥": "101220100",
            "郑州": "101180100",
            "长沙": "101250100",
            "福州": "101230100",
            "哈尔滨": "101050100",
            "济南": "101120100",
            "昆明": "101290100",
            "沈阳": "101090100"
        }
        
        # 热门搜索关键词
        self.keywords = [
            "Python", "Java", "前端", "后端", "算法", "数据分析", 
            "产品经理", "UI设计", "运营", "市场", "销售", "客服",
            "人事", "财务", "行政", "法务", "采购", "供应链",
            "项目经理", "测试", "运维", "安全", "架构师", "技术总监"
        ]
        
        # 薪资范围
        self.salary_ranges = {
            "不限": "",
            "3K以下": "402",
            "3-5K": "403", 
            "5-10K": "404",
            "10-15K": "405",
            "15-25K": "406", 
            "25-50K": "407",
            "50K以上": "408"
        }
        
        # 工作经验
        self.experience_levels = {
            "不限": "",
            "应届毕业生": "108",
            "1年以内": "102",
            "1-3年": "103",
            "3-5年": "104", 
            "5-10年": "105",
            "10年以上": "106"
        }
        
        # 学历要求
        self.education_levels = {
            "不限": "",
            "初中及以下": "209",
            "中专/中技": "208", 
            "高中": "206",
            "大专": "202",
            "本科": "203",
            "硕士": "204",
            "博士": "205"
        }
        
    def get_search_params(self, city: str = "全国", keyword: str = "", 
                         salary: str = "不限", experience: str = "不限", 
                         education: str = "不限", page: int = 1) -> Dict[str, Any]:
        """获取搜索参数"""
        params = {
            "city": self.cities.get(city, self.cities["全国"]),
            "query": keyword,
            "page": page
        }
        
        # 添加可选参数
        if salary != "不限" and salary in self.salary_ranges:
            params["salary"] = self.salary_ranges[salary]
            
        if experience != "不限" and experience in self.experience_levels:
            params["experience"] = self.experience_levels[experience]
            
        if education != "不限" and education in self.education_levels:
            params["degree"] = self.education_levels[education]
            
        return params
    
    def get_all_cities(self) -> List[str]:
        """获取所有城市列表"""
        return list(self.cities.keys())
    
    def get_all_keywords(self) -> List[str]:
        """获取所有关键词列表"""
        return self.keywords.copy()
    
    def get_comprehensive_search_configs(self) -> List[Dict[str, Any]]:
        """获取全面的搜索配置组合"""
        configs = []
        
        # 主要城市 + 热门关键词组合
        major_cities = ["北京", "上海", "广州", "深圳", "杭州", "成都"]
        hot_keywords = ["Python", "Java", "前端", "产品经理", "数据分析"]
        
        for city in major_cities:
            for keyword in hot_keywords:
                configs.append(self.get_search_params(city=city, keyword=keyword))
        
        # 全国范围的通用搜索
        for keyword in self.keywords:
            configs.append(self.get_search_params(city="全国", keyword=keyword))
            
        # 各城市的通用搜索（无关键词）
        for city in self.cities.keys():
            if city != "全国":
                configs.append(self.get_search_params(city=city, keyword=""))
        
        return configs
    
    def save_config(self, filename: str = "search_config.json"):
        """保存配置到文件"""
        config_data = {
            "cities": self.cities,
            "keywords": self.keywords,
            "salary_ranges": self.salary_ranges,
            "experience_levels": self.experience_levels,
            "education_levels": self.education_levels
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
    
    def load_config(self, filename: str = "search_config.json"):
        """从文件加载配置"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                
            self.cities = config_data.get("cities", self.cities)
            self.keywords = config_data.get("keywords", self.keywords)
            self.salary_ranges = config_data.get("salary_ranges", self.salary_ranges)
            self.experience_levels = config_data.get("experience_levels", self.experience_levels)
            self.education_levels = config_data.get("education_levels", self.education_levels)
            
        except FileNotFoundError:
            print(f"配置文件 {filename} 不存在，使用默认配置")
        except Exception as e:
            print(f"加载配置文件失败: {e}，使用默认配置")
