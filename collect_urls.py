#!/usr/bin/env python3
"""
BOSS直聘岗位URL收集器
自动获取所有真实且正在招聘中的岗位网址

使用方法:
    python collect_urls.py                    # 全面收集模式
    python collect_urls.py --quick           # 快速收集模式
    python collect_urls.py --keywords        # 关键词收集模式
    python collect_urls.py --validate        # 验证现有URL
    python collect_urls.py --help            # 显示帮助
"""

import asyncio
import sys
import os
import argparse
import time
from datetime import datetime

from url_collection import URLCollector, SearchConfig, URLValidator

def print_banner():
    """打印程序横幅"""
    print("🎯 BOSS直聘岗位URL收集器")
    print("=" * 60)
    print("✅ 自动获取所有真实招聘岗位网址")
    print("✅ 反反爬技术绕过限制")
    print("✅ 高效并发抓取")
    print("✅ URL有效性验证")
    print("=" * 60)

def print_usage():
    """打印使用说明"""
    print("📋 使用方法:")
    print("  python collect_urls.py                    # 全面收集模式")
    print("  python collect_urls.py --quick           # 快速收集模式(1000个URL)")
    print("  python collect_urls.py --keywords        # 关键词收集模式")
    print("  python collect_urls.py --validate        # 验证现有URL")
    print("  python collect_urls.py --cities          # 显示支持的城市")
    print("  python collect_urls.py --help            # 显示帮助")
    print("")

async def full_collection_mode():
    """全面收集模式"""
    print("🚀 全面收集模式")
    print("-" * 40)
    
    collector = URLCollector()
    
    try:
        # 收集所有URL
        urls = await collector.collect_all_job_urls(
            max_pages_per_search=20,
            max_concurrent_searches=2
        )
        
        if urls:
            # 验证URL
            print("\n🔍 开始验证URL有效性...")
            validation_result = await collector.url_validator.batch_validate_urls(urls)
            valid_urls = validation_result["valid"]
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"boss_job_urls_{timestamp}.txt"
            
            collector.save_urls_to_file(valid_urls, output_file)
            collector.save_collection_report(valid_urls, f"collection_report_{timestamp}.json")
            collector.url_validator.save_validation_cache(f"validation_cache_{timestamp}.txt")
            
            print(f"\n🎉 收集完成!")
            print(f"📊 总计收集: {len(urls)} 个URL")
            print(f"📊 验证有效: {len(valid_urls)} 个URL")
            print(f"📄 输出文件: {output_file}")
            
        else:
            print("❌ 没有收集到任何URL")
            
    except Exception as e:
        print(f"❌ 收集过程异常: {e}")

async def quick_collection_mode():
    """快速收集模式"""
    print("⚡ 快速收集模式")
    print("-" * 40)
    
    collector = URLCollector()
    
    try:
        # 快速收集1000个URL
        urls = await collector.quick_collect(max_urls=1000)
        
        if urls:
            # 简单验证
            print("\n🔍 验证URL格式...")
            valid_urls = [url for url in urls if collector.url_validator.is_valid_format(url)]
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"boss_job_urls_quick_{timestamp}.txt"
            
            collector.save_urls_to_file(valid_urls, output_file)
            
            print(f"\n🎉 快速收集完成!")
            print(f"📊 收集到: {len(valid_urls)} 个有效URL")
            print(f"📄 输出文件: {output_file}")
            
        else:
            print("❌ 没有收集到任何URL")
            
    except Exception as e:
        print(f"❌ 快速收集异常: {e}")

async def keywords_collection_mode():
    """关键词收集模式"""
    print("🔍 关键词收集模式")
    print("-" * 40)
    
    # 获取用户输入
    keywords_input = input("请输入关键词(用逗号分隔，回车使用默认): ").strip()
    cities_input = input("请输入城市(用逗号分隔，回车使用默认): ").strip()
    
    collector = URLCollector()
    
    # 处理关键词
    if keywords_input:
        keywords = [k.strip() for k in keywords_input.split(',') if k.strip()]
    else:
        keywords = ["Python", "Java", "前端", "产品经理", "数据分析"]
    
    # 处理城市
    if cities_input:
        cities = [c.strip() for c in cities_input.split(',') if c.strip()]
    else:
        cities = ["北京", "上海", "广州", "深圳"]
    
    print(f"🎯 关键词: {keywords}")
    print(f"🏙️  城市: {cities}")
    
    try:
        # 根据关键词收集
        urls = await collector.collect_urls_by_keywords(keywords, cities, max_pages=10)
        
        if urls:
            # 验证URL
            print("\n🔍 验证URL有效性...")
            validation_result = await collector.url_validator.batch_validate_urls(urls)
            valid_urls = validation_result["valid"]
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"boss_job_urls_keywords_{timestamp}.txt"
            
            collector.save_urls_to_file(valid_urls, output_file)
            
            print(f"\n🎉 关键词收集完成!")
            print(f"📊 收集到: {len(valid_urls)} 个有效URL")
            print(f"📄 输出文件: {output_file}")
            
        else:
            print("❌ 没有收集到任何URL")
            
    except Exception as e:
        print(f"❌ 关键词收集异常: {e}")

async def validate_existing_urls():
    """验证现有URL"""
    print("🔍 验证现有URL")
    print("-" * 40)
    
    # 查找现有的URL文件
    url_files = [f for f in os.listdir('.') if f.startswith('boss_job_urls') and f.endswith('.txt')]
    
    if not url_files:
        print("❌ 没有找到现有的URL文件")
        return
    
    print("📄 找到以下URL文件:")
    for i, file in enumerate(url_files, 1):
        print(f"  {i}. {file}")
    
    try:
        choice = int(input("请选择要验证的文件编号: ")) - 1
        if 0 <= choice < len(url_files):
            file_path = url_files[choice]
        else:
            print("❌ 无效选择")
            return
    except ValueError:
        print("❌ 无效输入")
        return
    
    # 读取URL
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip()]
        
        print(f"📊 读取到 {len(urls)} 个URL")
        
        # 验证URL
        validator = URLValidator()
        validation_result = await validator.batch_validate_urls(urls)
        
        valid_urls = validation_result["valid"]
        invalid_urls = validation_result["invalid"]
        
        # 保存验证结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if valid_urls:
            valid_file = f"boss_job_urls_validated_{timestamp}.txt"
            with open(valid_file, 'w', encoding='utf-8') as f:
                for url in valid_urls:
                    f.write(f"{url}\n")
            print(f"✅ 有效URL已保存到: {valid_file}")
        
        if invalid_urls:
            invalid_file = f"invalid_urls_{timestamp}.txt"
            with open(invalid_file, 'w', encoding='utf-8') as f:
                for url in invalid_urls:
                    f.write(f"{url}\n")
            print(f"❌ 无效URL已保存到: {invalid_file}")
        
        print(f"\n📊 验证结果:")
        print(f"  有效URL: {len(valid_urls)}")
        print(f"  无效URL: {len(invalid_urls)}")
        print(f"  有效率: {len(valid_urls)/(len(valid_urls)+len(invalid_urls))*100:.1f}%")
        
    except Exception as e:
        print(f"❌ 验证过程异常: {e}")

def show_supported_cities():
    """显示支持的城市"""
    print("🏙️  支持的城市列表:")
    print("-" * 40)
    
    config = SearchConfig()
    cities = config.get_all_cities()
    
    for i, city in enumerate(cities, 1):
        print(f"  {i:2d}. {city}")
    
    print(f"\n📊 总计支持 {len(cities)} 个城市")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BOSS直聘岗位URL收集器")
    parser.add_argument('--quick', action='store_true', help='快速收集模式')
    parser.add_argument('--keywords', action='store_true', help='关键词收集模式')
    parser.add_argument('--validate', action='store_true', help='验证现有URL')
    parser.add_argument('--cities', action='store_true', help='显示支持的城市')
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.cities:
        show_supported_cities()
        return
    
    if args.validate:
        await validate_existing_urls()
        return
    
    if args.keywords:
        await keywords_collection_mode()
        return
    
    if args.quick:
        await quick_collection_mode()
        return
    
    # 默认全面收集模式
    if len(sys.argv) == 1:
        print("🎯 选择收集模式:")
        print("1. 全面收集模式 (推荐)")
        print("2. 快速收集模式")
        print("3. 关键词收集模式")
        print("4. 验证现有URL")
        print("5. 显示支持城市")
        
        try:
            choice = input("\n请选择模式 (1-5): ").strip()
            
            if choice == "1":
                await full_collection_mode()
            elif choice == "2":
                await quick_collection_mode()
            elif choice == "3":
                await keywords_collection_mode()
            elif choice == "4":
                await validate_existing_urls()
            elif choice == "5":
                show_supported_cities()
            else:
                print("❌ 无效选择")
                print_usage()
        except KeyboardInterrupt:
            print("\n\n👋 程序已中断")
    else:
        await full_collection_mode()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 程序已中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
